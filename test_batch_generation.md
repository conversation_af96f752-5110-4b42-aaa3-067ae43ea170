# 批次号生成功能测试文档

## 功能概述
实现了来料确认时的批次号自动生成功能，支持手动输入和自动生成两种模式。

## 主要修改

### 1. MaterialArrivalDto.java
- 添加了可选的 `batch` 字段
- 支持手动输入批次号，如果为空则自动生成

### 2. BasicDocumentInfoService.java
- 添加了 `generateBatchNumber()` 方法
- 添加了 `generateNextBatchNumber()` 辅助方法
- 修改了 `createBatchProcessRecord()` 方法，支持批次号处理

### 3. DocumentExecuteService.java
- 修改了入库逻辑，从 DocumentInventoryDetail 获取正确的批次号
- 避免硬编码"默认批次"覆盖生成的批次号

### 4. DocumentInfoController.java
- 添加了测试接口 `/generateBatchNumber` 用于测试批次号生成

## 批次号生成规则
- 格式：LOT + yyyyMMdd + 3位流水号
- 示例：LOT20250801-001, LOT20250801-002
- 每日重置流水号，从001开始
- 使用 synchronized 保证线程安全

## 测试方法

### 1. 测试批次号生成
```bash
POST /speedbot/basicData/document/generateBatchNumber
```

### 2. 测试来料确认（手动批次）
```json
{
  "documentId": "单据ID",
  "arrivalList": [
    {
      "detailId": "明细ID",
      "materialCode": "物料编码",
      "arrivalQuantity": 100,
      "batch": "LOT20250801-001",
      "remark": "手动输入批次"
    }
  ]
}
```

### 3. 测试来料确认（自动生成批次）
```json
{
  "documentId": "单据ID", 
  "arrivalList": [
    {
      "detailId": "明细ID",
      "materialCode": "物料编码",
      "arrivalQuantity": 100,
      "remark": "自动生成批次"
    }
  ]
}
```

## 预期结果
1. 手动输入批次时，使用用户提供的批次号
2. 未输入批次时，自动生成格式为 LOTyyyyMMdd-xxx 的批次号
3. 每个物料(SKU)都有独立的批次号
4. 批次号在整个流程中保持一致（来料确认 -> 质检 -> 入库）

## 注意事项
1. 批次号生成使用了 synchronized 关键字保证线程安全
2. 查询最大批次号时使用了 DocumentInventoryDetail 表
3. 实际入库时会从 DocumentInventoryDetail 获取批次号设置到 BasicMaterialBatchInventory
