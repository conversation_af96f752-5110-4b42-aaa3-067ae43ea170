package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.domain.basicData.BasicDocumentDetail;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.DocumentInventoryDetail;
import com.ruoyi.domain.bill.*;
import com.ruoyi.domain.qc.QcTemplateMaterial;
import com.ruoyi.mapper.basicData.BasicDocumentDetailMapper;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper;
import com.ruoyi.mapper.basicData.DocumentInventoryDetailMapper;
import com.ruoyi.service.bill.*;
import com.ruoyi.service.qc.QcTemplateMaterialService;
import com.ruoyi.service.qc.QcWorkTaskService;
import com.ruoyi.service.work.LkSystemService;
import com.ruoyi.utils.*;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.utils.constant.DocumentStatusConstant;
import com.ruoyi.utils.constant.InterConstant;
import com.ruoyi.vo.basicData.BasicDocumentInfoDto;
import com.ruoyi.vo.basicData.DocumentDetailDto;
import com.ruoyi.vo.basicData.MaterialArrivalDto;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BasicDocumentInfoService extends ServiceImpl<BasicDocumentInfoMapper, BasicDocumentInfo> {

    protected final Logger logger = LoggerFactory.getLogger(BasicDocumentInfoService.class);

    @Resource
    private BasicDocumentInfoMapper basicDocumentInfoMapper;

    @Resource
    BasicDocumentDetailMapper basicDocumentDetailMapper;
    @Resource
    BasicDocumentDetailService basicDocumentDetailService;

    @Resource
    LkSystemService lkSystemService;

    @Resource
    BasicMaterialBatchInventoryMapper basicMaterialBatchInventoryMapper;

    @Resource
    DocumentInventoryDetailMapper documentInventoryDetailMapper;

    @Resource
    QcWorkTaskService qcWorkTaskService;

    @Resource
    QcTemplateMaterialService qcTemplateMaterialService;

    @Resource
    private PurchaseOrderDetailService purchaseOrderDetailService;

    @Resource
    private ReceiveNoticeDetailService receiveNoticeDetailService;

    @Resource
    private ReturnMaterialDetailService returnMaterialDetailService;

    @Resource
    private ReturnNoticeDetailService returnNoticeDetailService;

    @Resource
    private DeliveryNoticeDetailService deliveryNoticeDetailService;

    @Resource
    private ProductionPickingMaterialDetailService productionPickingMaterialDetailService;

    @Resource
    private ProductionFeedMaterialDetailService productionFeedMaterialDetailService;

    @Resource
    private ProductionInStockDetailService productionInStockDetailService;

    @Resource
    private SimpleProductionPickingMaterialDetailService simpleProductionPickingMaterialDetailService;

    @Resource
    private SimpleProductionInStockDetailService simpleProductionInStockDetailService;

    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;

    public synchronized String getMaxIndex(String headstr) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        QueryWrapper<BasicDocumentInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("transaction_code", headstr + strDate);
        queryWrapper.orderByDesc("transaction_code");
        queryWrapper.last("LIMIT 1");
        BasicDocumentInfo task = this.basicDocumentInfoMapper.selectOne(queryWrapper);
        String mxstr;
        if (task == null) {
            mxstr = headstr + DateAndTimeUtil.getNowTimeNoSeparator();
        }else {
            mxstr = task.getTransactionCode();
        }
        return an.getNum(headstr, mxstr);
    }

    /**
     * 生成批次号
     * @return 生成的批次号
     */
    public synchronized String generateBatchNumber(String headstr) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate(); // 获取yyyyMMdd格式日期

        // 查询DocumentInventoryDetail表中当天最大的批次号
        QueryWrapper<DocumentInventoryDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("inventory_code", headstr + strDate);
        queryWrapper.orderByDesc("inventory_code");
        queryWrapper.last("LIMIT 1");

        DocumentInventoryDetail maxBatch = documentInventoryDetailMapper.selectOne(queryWrapper);
        String maxBatchStr;
        if (maxBatch == null || maxBatch.getInventoryCode() == null) {
            maxBatchStr = headstr + DateAndTimeUtil.getNowTimeNoSeparator();
        } else {
            maxBatchStr = maxBatch.getInventoryCode();
        }

        return an.getNum(headstr, maxBatchStr);
    }



    /**
     * 查询单据信息（支持物料查询、供应商/客户名称查询）
     * @param queryParamVO 增强查询参数
     * @return 单据信息列表
     */
    public List<BasicDocumentInfo> queryDocumentInfoEnhanced(DocumentQueryParamVO queryParamVO) {
        List<BasicDocumentInfo> result = basicDocumentInfoMapper.queryDocumentInfoEnhanced(queryParamVO);
        return result;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult addDocumentInfo(BasicDocumentInfoDto param) {
        BasicDocumentInfo basicDocumentInfo = new BasicDocumentInfo();
        BeanUtils.copyProperties(param, basicDocumentInfo);
        String id = UUID.randomUUID().toString();
        basicDocumentInfo.setId(id);
        String prefix = getPrefixByType(param.getBusinessType());
        if (prefix == null){
            return ResponseResult.getErrorResult("未定义该单据类型:"+param.getBusinessType());
        }
        String transactionCode = getMaxIndex(prefix);
        basicDocumentInfo.setTransactionCode(transactionCode);
        basicDocumentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        basicDocumentInfo.setBusinessSource(CommonConstant.BusinessSource.WEB);
        basicDocumentInfo.setSupplySalesCode(param.getSupplySalesCode());
        basicDocumentInfo.setIsLock(CommonConstant.IsLock.UN_LOCK);
        basicDocumentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH);
        basicDocumentInfo.setCreateName(SecurityUtils.getUsername());
        basicDocumentInfo.setSupplySalesCode(param.getSupplySalesCode());
        basicDocumentInfoMapper.insert(basicDocumentInfo);
        List<DocumentDetailDto> details = param.getDetails();
        if (details != null && details.size() > 0) {
            for (DocumentDetailDto documentDetailDto : details) {
                BasicDocumentDetail basicDocumentDetail = new BasicDocumentDetail();
                BeanUtils.copyProperties(documentDetailDto, basicDocumentDetail);
                String detailId = UUID.randomUUID().toString();
                basicDocumentDetail.setId(detailId);
                basicDocumentDetail.setCompletedNum(0);
                basicDocumentDetail.setDocumentCode(id);
                basicDocumentDetailMapper.insert(basicDocumentDetail);
            }
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 根据单据类型获取单据前缀
     * @return
     */
    public String getPrefixByType(Integer businessType) {
        if (businessType == null) {
            return null;
        }
        if (businessType.equals(CommonConstant.BoundType.SCLL)) {
            return CommonConstant.CodePrefix.PRODUCE_PICK;
        } else if (businessType.equals(CommonConstant.BoundType.SCBL)) {
            return CommonConstant.CodePrefix.PRODUCE_REPLENISH;
        } else if (businessType.equals(CommonConstant.BoundType.SCRK)) {
            return CommonConstant.CodePrefix.PRODUCE_IN;
        } else if (businessType.equals(CommonConstant.BoundType.SCTL)) {
            return CommonConstant.CodePrefix.PRODUCE_RETURN;
        } else if (businessType.equals(CommonConstant.BoundType.CGRK)) {
            return CommonConstant.CodePrefix.PURCHASE_IN;
        } else if (businessType.equals(CommonConstant.BoundType.CGTH)) {
            return CommonConstant.CodePrefix.PURCHASE_RETURN;
        } else if (businessType.equals(CommonConstant.BoundType.XSCK)) {
            return CommonConstant.CodePrefix.SALES_OUT;
        } else if (businessType.equals(CommonConstant.BoundType.XSTH)) {
            return CommonConstant.CodePrefix.SALES_RETURN;
        } else if (businessType.equals(CommonConstant.BoundType.WLDB)) {
            return CommonConstant.CodePrefix.MATERIAL_ALLOT_PREFIX;
        } else if (businessType.equals(CommonConstant.BoundType.KCPD)) {
            return CommonConstant.CodePrefix.INVENTORY_PREFIX;
        } else if (businessType.equals(CommonConstant.BoundType.CKYL)) {
            return CommonConstant.CodePrefix.WAREHOUSE_OUTBOUND_PREFIX;
        } else {
            return null;
        }
    }
    /**
     * 更新
     */
    @Transactional
    public ResponseResult updateDocumentInfo(BasicDocumentInfoDto param) {
        // 获取单据信息
        BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.selectById(param.getId());
        if (basicDocumentInfo == null) {
            return ResponseResult.getErrorResult("单据不存在,请选择相应单据");
        }
        if (basicDocumentInfo.getIsLock() == CommonConstant.IsLock.LOCK) {
            return ResponseResult.getErrorResult("单据已锁定，不允许修改");
        }
        List<DocumentDetailDto> details = param.getDetails();
        if (details != null && !details.isEmpty()) {
            for (DocumentDetailDto documentDetailDto : details) {
                if (basicDocumentInfo.getTransactionType() == CommonConstant.InoutType.OUT) {
                    // 判断物料库存是否足够
                    Integer num = basicMaterialBatchInventoryService.queryMaterialNumByCode(documentDetailDto.getMaterialCode());
                    if (num < documentDetailDto.getQuantity()) {
                        return ResponseResult.getErrorResult(documentDetailDto.getMaterialCode() + "物料库存数量为:" + num + "小于出库数量" +
                                documentDetailDto.getQuantity() + "请修改数量!");
                    }
                }
                BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetailDto.getId());
                basicDocumentDetail.setQuantity(documentDetailDto.getQuantity());
                basicDocumentDetailMapper.updateById(basicDocumentDetail);
            }
        }

        // 处理锁定状态和库存分配
        if (CommonConstant.IsLock.LOCK == param.getIsLock() && basicDocumentInfo.getTransactionType() == CommonConstant.InoutType.OUT) {
            lockDocumentAndUpdateInventory(basicDocumentInfo);
        }
        // 更新主单据信息
        basicDocumentInfo.setIsLock(param.getIsLock());
        basicDocumentInfo.setSupplySalesCode(param.getSupplySalesCode());
        basicDocumentInfoMapper.updateById(basicDocumentInfo);

        return ResponseResult.getSuccessResult();
    }


    /**
     * 锁单库存物料分配
     */
    private void lockDocumentAndUpdateInventory(BasicDocumentInfo basicDocumentInfo) {
        List<BasicDocumentDetail> basicDocumentDetails = basicDocumentDetailMapper.selectByDocumentCode(basicDocumentInfo.getId());
        for (BasicDocumentDetail basicDocumentDetail : basicDocumentDetails) {
            // 查询物料批次
            List<BasicMaterialBatchInventory> availBatchByMaterialCode = basicMaterialBatchInventoryMapper.getAvailBatchByMaterialCode(basicDocumentDetail.getMaterialCode());

            // 查询立库数据
            Integer boxInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.BOX_INVENTORY_STATISTICS, basicDocumentDetail.getMaterialCode());
            Integer plateInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PLATE_INVENTORY_STATISTICS, basicDocumentDetail.getMaterialCode());
            Integer profileInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PROFILE_INVENTORY_STATISTICS, basicDocumentDetail.getMaterialCode());

            this.inventoryAddLk(availBatchByMaterialCode, boxInteger, CommonConstant.LkName.BOX);
            this.inventoryAddLk(availBatchByMaterialCode, plateInteger, CommonConstant.LkName.PLATE);
            this.inventoryAddLk(availBatchByMaterialCode, profileInteger, CommonConstant.LkName.PROFILE);

            // 遍历已排序的批次列表进行库存分配
            allocateInventoryToDocument(basicDocumentDetail, availBatchByMaterialCode);

            // 根据单据类型处理质检
            handleQcTasks(basicDocumentInfo, basicDocumentDetail);
        }
    }

    /**
     * 批次列表进行库存分配
     */
    private void allocateInventoryToDocument(BasicDocumentDetail basicDocumentDetail, List<BasicMaterialBatchInventory> availBatchByMaterialCode) {
        int remainingQty = basicDocumentDetail.getQuantity();
        for (BasicMaterialBatchInventory batch : availBatchByMaterialCode) {
            if (remainingQty <= 0) break;

            // 计算当前批次可分配的数量
            int allocatableQty = Math.min(remainingQty, batch.getAvailNum());
            if (allocatableQty > 0) {
                // 创建分配记录
                DocumentInventoryDetail documentInventoryDetail = new DocumentInventoryDetail();
                documentInventoryDetail.setId(UUID.randomUUID().toString());
                documentInventoryDetail.setDetailCode(basicDocumentDetail.getId());
                documentInventoryDetail.setMaterialCode(basicDocumentDetail.getMaterialCode());
                documentInventoryDetail.setQuantity(allocatableQty);
                documentInventoryDetail.setCompletedNum(0);
                if (StringUtils.isNotEmpty(batch.getId())) {
                    documentInventoryDetail.setInventoryCode(batch.getContainerCode());
                    // 冻结库存
                    batch.setAvailNum(batch.getAvailNum() - allocatableQty);
                    batch.setFreezeNum(batch.getFreezeNum() + allocatableQty);
                    basicMaterialBatchInventoryMapper.updateByPrimaryKeySelective(batch);
                } else {
                    documentInventoryDetail.setInventoryCode(batch.getContainerCode());
                }
                documentInventoryDetailMapper.insert(documentInventoryDetail);
                // 更新剩余需求量
                remainingQty -= allocatableQty;
            }
        }
    }

    /**
     * 出入库质检任务增加
     */
    private void handleQcTasks(BasicDocumentInfo basicDocumentInfo, BasicDocumentDetail basicDocumentDetail) {
        if (basicDocumentInfo.getTransactionType() == CommonConstant.BoundType.CGRK || basicDocumentInfo.getTransactionType() == CommonConstant.BoundType.XSTH) {
            // 入库质检
            qcWorkTaskService.saveQcIpqcInfo(basicDocumentDetail.getMaterialCode(), basicDocumentDetail.getQuantity(),
                    basicDocumentInfo.getSupplySalesCode(), "入库质检", basicDocumentInfo.getTransactionCode(), 4);
        }
        if (basicDocumentInfo.getTransactionType() == CommonConstant.BoundType.CGTH || basicDocumentInfo.getTransactionType() == CommonConstant.BoundType.XSCK) {
            // 出库质检
            qcWorkTaskService.saveQcIpqcInfo(basicDocumentDetail.getMaterialCode(), basicDocumentDetail.getQuantity(),
                    basicDocumentInfo.getSupplySalesCode(), "出库质检", basicDocumentInfo.getTransactionCode(), 1);
        }
    }


    private void inventoryAddLk(List<BasicMaterialBatchInventory> availBatchByMaterialCode, Integer num, String lkName) {
        if (num != null && num > 0) {
            BasicMaterialBatchInventory basicMaterialBatchInventory = new BasicMaterialBatchInventory();
            basicMaterialBatchInventory.setAvailNum(num);
            basicMaterialBatchInventory.setContainerCode(lkName);
            availBatchByMaterialCode.add(basicMaterialBatchInventory);
        }
    }

    /**
     * 删除
     */
    public ResponseResult delDocumentInfo(BatchIdsReq req) {
        List<String> ids = req.getIds();
        for (String idTemp : ids) {
            BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.selectById(idTemp);
            if (basicDocumentInfo != null && basicDocumentInfo.getIsLock() == CommonConstant.IsLock.LOCK) {
                continue;
            }
            basicDocumentInfoMapper.deleteById(idTemp);
            basicDocumentDetailMapper.deleteByCode(idTemp);
        }
        return ResponseResult.getSuccessResult();
    }

    //***********************************************通用单据生成方法**********************************************

    /**
     * 通用批量单据生成方法
     * 根据不同的来源单据类型，批量生成对应的WMS单据
     *
     * @param sourceType         来源单据类型（PURCHASE_ORDER-采购订单, DELIVERY_NOTICE-发货通知等）
     * @param sourceDataList     来源单据数据列表
     * @param targetBusinessType 目标单据业务类型
     * @param transactionType    事务类型（入库/出库）
     * @return 生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createDocumentFromSourceBatch(String sourceType, List<?> sourceDataList,
                                                        Integer targetBusinessType, Integer transactionType) {
        logger.info("开始批量创建单据，来源类型：{}，数据数量：{}，目标业务类型：{}，事务类型：{}",
                sourceType, sourceDataList.size(), targetBusinessType, transactionType);

        if (sourceDataList.isEmpty()) {
            return ResponseResult.getSuccessResult("无数据需要处理", null);
        }

        try {
            switch (sourceType.toUpperCase()) {
                case CommonConstant.SourceDocumentType.PURCHASE_ORDER:
                    @SuppressWarnings("unchecked")
                    List<PurchaseOrder> purchaseOrders = (List<PurchaseOrder>) sourceDataList;
                    return createPurchaseInboundDocumentsBatch(purchaseOrders);

                case CommonConstant.SourceDocumentType.DELIVERY_NOTICE:
                    @SuppressWarnings("unchecked")
                    List<DeliveryNotice> deliveryNotices = (List<DeliveryNotice>) sourceDataList;
                    return createSalesOutboundDocumentsBatch(deliveryNotices);

                case CommonConstant.SourceDocumentType.PRODUCTION_PICKING_MATERIAL:
                    @SuppressWarnings("unchecked")
                    List<ProductionPickingMaterial> productionPickingMaterials = (List<ProductionPickingMaterial>) sourceDataList;
                    return createProductionOutboundDocumentsBatch(productionPickingMaterials, "生产领料单");

                case CommonConstant.SourceDocumentType.PRODUCTION_FEED_MATERIAL:
                    @SuppressWarnings("unchecked")
                    List<ProductionFeedMaterial> productionFeedMaterials = (List<ProductionFeedMaterial>) sourceDataList;
                    return createProductionFeedOutboundDocumentsBatch(productionFeedMaterials);

                case CommonConstant.SourceDocumentType.PRODUCTION_IN_STOCK:
                    @SuppressWarnings("unchecked")
                    List<ProductionInStock> productionInStocks = (List<ProductionInStock>) sourceDataList;
                    return createProductionInboundDocumentsBatch(productionInStocks);

                case CommonConstant.SourceDocumentType.SIMPLE_PRODUCTION_PICKING_MATERIAL:
                    @SuppressWarnings("unchecked")
                    List<SimpleProductionPickingMaterial> simpleProductionPickingMaterials = (List<SimpleProductionPickingMaterial>) sourceDataList;
                    return createSimpleProductionOutboundDocumentsBatch(simpleProductionPickingMaterials);

                case CommonConstant.SourceDocumentType.SIMPLE_PRODUCTION_IN_STOCK:
                    @SuppressWarnings("unchecked")
                    List<SimpleProductionInStock> simpleProductionInStocks = (List<SimpleProductionInStock>) sourceDataList;
                    return createSimpleProductionInboundDocumentsBatch(simpleProductionInStocks);

                default:
                    logger.warn("不支持的来源单据类型：{}", sourceType);
                    return ResponseResult.getErrorResult("不支持的来源单据类型：" + sourceType);
            }
        } catch (Exception e) {
            logger.error("批量创建单据失败，来源类型：{}，数据数量：{}", sourceType, sourceDataList.size(), e);
            return ResponseResult.getErrorResult("批量创建单据失败：" + e.getMessage());
        }
    }

    /**
     * 批量创建采购入库单据
     *
     * @param purchaseOrders 采购订单列表
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createPurchaseInboundDocumentsBatch(List<PurchaseOrder> purchaseOrders) {
        logger.info("开始批量创建采购入库单据，采购订单数量：{}", purchaseOrders.size());

        if (purchaseOrders.isEmpty()) {
            return ResponseResult.getSuccessResult("无需创建入库单据", null);
        }

        try {
            // 1. 批量获取采购订单ID列表
            List<String> orderIds = purchaseOrders.stream()
                    .map(PurchaseOrder::getId)
                    .collect(Collectors.toList());

            // 2. 批量获取所有采购订单明细
            List<PurchaseOrderDetail> allOrderDetails = purchaseOrderDetailService.lambdaQuery()
                    .in(PurchaseOrderDetail::getOrderId, orderIds)
                    .list();

            // 3. 按订单ID分组明细
            Map<String, List<PurchaseOrderDetail>> detailsGroupByOrderId = allOrderDetails.stream()
                    .collect(Collectors.groupingBy(PurchaseOrderDetail::getOrderId));

            // 4. 批量创建单据主信息和明细
            List<BasicDocumentInfo> documentInfoList = new ArrayList<>();
            List<BasicDocumentDetail> allDocumentDetails = new ArrayList<>();

            for (PurchaseOrder purchaseOrder : purchaseOrders) {
                // 创建单据主信息
                BasicDocumentInfo documentInfo = mapPurchaseOrderToDocument(purchaseOrder);
                String documentId = UUID.randomUUID().toString();
                documentInfo.setId(documentId);
                documentInfoList.add(documentInfo);

                // 创建单据明细
                List<PurchaseOrderDetail> orderDetails = detailsGroupByOrderId.get(purchaseOrder.getId());
                if (orderDetails != null && !orderDetails.isEmpty()) {
                    for (PurchaseOrderDetail orderDetail : orderDetails) {
                        BasicDocumentDetail detail = mapPurchaseOrderDetailToDocumentDetail(orderDetail, documentId);
                        allDocumentDetails.add(detail);
                    }
                }
            }

            // 5. 批量保存单据主信息
            for (BasicDocumentInfo documentInfo : documentInfoList) {
                basicDocumentInfoMapper.insert(documentInfo);
            }

            // 6. 批量保存单据明细
            for (BasicDocumentDetail detail : allDocumentDetails) {
                basicDocumentDetailMapper.insert(detail);
            }

            logger.info("批量创建采购入库单据成功，单据数量：{}，明细数量：{}",
                    documentInfoList.size(), allDocumentDetails.size());

            return ResponseResult.getSuccessResult(
                    String.format("批量创建采购入库单据成功，单据数量：%d", documentInfoList.size()),
                    documentInfoList.size());

        } catch (Exception e) {
            logger.error("批量创建采购入库单据失败", e);
            throw e; // 让事务回滚
        }
    }

    /**
     * 批量创建销售出库单据
     *
     * @param deliveryNotices 发货通知单列表
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createSalesOutboundDocumentsBatch(List<DeliveryNotice> deliveryNotices) {

        log.info("开始批量创建销售出库单据，发货通知单数量：{}", deliveryNotices.size());

        if (deliveryNotices.isEmpty()) {
            return ResponseResult.getSuccessResult("无需创建出库单据", null);
        }

        try {
            // 批量获取发货通知单ID列表
            List<String> noticeIds = deliveryNotices.stream()
                    .map(DeliveryNotice::getId)
                    .collect(Collectors.toList());

            // 批量获取所有发货通知单明细
            List<DeliveryNoticeDetail> allNoticeDetails = deliveryNoticeDetailService.lambdaQuery()
                    .in(DeliveryNoticeDetail::getNoticeId, noticeIds)
                    .list();

            // 按通知单ID分组明细
            Map<String, List<DeliveryNoticeDetail>> detailsGroupByNoticeId = allNoticeDetails.stream()
                    .collect(Collectors.groupingBy(DeliveryNoticeDetail::getNoticeId));

            // 批量创建单据主信息和明细
            List<BasicDocumentInfo> documentInfoList = new ArrayList<>();
            List<BasicDocumentDetail> allDocumentDetails = new ArrayList<>();

            for (DeliveryNotice deliveryNotice : deliveryNotices) {
                // 创建单据主信息
                BasicDocumentInfo documentInfo = mapDeliveryNoticeToDocument(deliveryNotice);
                String documentId = UUID.randomUUID().toString();
                documentInfo.setId(documentId);
                documentInfoList.add(documentInfo);

                // 创建单据明细
                List<DeliveryNoticeDetail> noticeDetails = detailsGroupByNoticeId.get(deliveryNotice.getId());
                if (noticeDetails != null && !noticeDetails.isEmpty()) {
                    for (DeliveryNoticeDetail noticeDetail : noticeDetails) {
                        BasicDocumentDetail detail = mapDeliveryNoticeDetailToDocumentDetail(noticeDetail, documentId);
                        allDocumentDetails.add(detail);
                    }
                }
            }

            // 批量保存单据主信息
            for (BasicDocumentInfo documentInfo : documentInfoList) {
                basicDocumentInfoMapper.insert(documentInfo);
            }

            // 批量保存单据明细
            for (BasicDocumentDetail detail : allDocumentDetails) {
                basicDocumentDetailMapper.insert(detail);
            }
            log.info("批量创建销售出库单据成功，单据数量：{}，明细数量：{}",
                    documentInfoList.size(), allDocumentDetails.size());
            // 分配库存
            for (BasicDocumentInfo documentInfo : documentInfoList) {
                lockDocumentAndUpdateInventory(documentInfo);
            }
            return ResponseResult.getSuccessResult();
        } catch (Exception e) {
            log.error("批量创建销售出库单据失败", e);
            throw e; // 让事务回滚
        }
    }

    /**
     * 将采购订单映射为单据主信息
     *
     * @param purchaseOrder 采购订单
     * @return 单据主信息
     */
    private BasicDocumentInfo mapPurchaseOrderToDocument(PurchaseOrder purchaseOrder) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.CGRK);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(purchaseOrder.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.IN); // 入库
        documentInfo.setBusinessType(CommonConstant.BoundType.CGRK);  // 采购入库
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定
        documentInfo.setSupplySalesCode(purchaseOrder.getSupplierId());
        return documentInfo;
    }

    /**
     * 将采购订单明细映射为单据明细
     *
     * @param orderDetail 采购订单明细
     * @param documentId  单据ID
     * @return 单据明细
     */
    private BasicDocumentDetail mapPurchaseOrderDetailToDocumentDetail(PurchaseOrderDetail orderDetail, String documentId) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();
        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentId);
        documentDetail.setMaterialCode(orderDetail.getMaterialId());
        Integer quantity = orderDetail.getQty() != null ? orderDetail.getQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0); // 初始完成数量为0
        logger.debug("采购订单明细转换完成，物料编码：{}，数量：{}", orderDetail.getMaterialId(), quantity);
        return documentDetail;
    }

    /**
     * 将发货通知单映射为单据主信息
     *
     * @param deliveryNotice 发货通知单
     * @return 单据主信息
     */
    private BasicDocumentInfo mapDeliveryNoticeToDocument(DeliveryNotice deliveryNotice) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.XSCK);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(deliveryNotice.getBillNo());

        // 设置单据类型信息
        documentInfo.setTransactionType(CommonConstant.InoutType.OUT); // 出库
        documentInfo.setBusinessType(CommonConstant.BoundType.XSCK);   // 销售出库
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源

        // 设置时间和状态
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK);

        // 设置客户信息
        documentInfo.setSupplySalesCode(deliveryNotice.getCustomerId());

        logger.debug("发货通知单映射为单据主信息完成，WMS单据编号：{}，来源单据编号：{}",
                transactionCode, deliveryNotice.getBillNo());
        return documentInfo;
    }

    /**
     * 将发货通知单明细映射为单据明细
     *
     * @param noticeDetail 发货通知单明细
     * @param documentId   单据ID
     * @return 单据明细
     */
    private BasicDocumentDetail mapDeliveryNoticeDetailToDocumentDetail(DeliveryNoticeDetail noticeDetail, String documentId) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentId);
        documentDetail.setMaterialCode(noticeDetail.getMaterialId());

        // 数量转换：BigDecimal转Integer
        Integer quantity = noticeDetail.getQty() != null ? noticeDetail.getQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0); // 初始完成数量为0

        logger.debug("发货通知单明细映射完成，物料编码：{}，数量：{}", noticeDetail.getMaterialId(), quantity);
        return documentDetail;
    }

    /**
     * 批量创建生产出库单据（生产领料单）
     *
     * @param productionPickingMaterials 生产领料单列表
     * @param sourceTypeName             来源单据类型名称
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createProductionOutboundDocumentsBatch(List<ProductionPickingMaterial> productionPickingMaterials, String sourceTypeName) {
        logger.info("开始批量创建生产出库单据，{}数量：{}", sourceTypeName, productionPickingMaterials.size());

        List<BasicDocumentInfo> documentsToInsert = new ArrayList<>();
        List<BasicDocumentDetail> detailsToInsert = new ArrayList<>();

        for (ProductionPickingMaterial picking : productionPickingMaterials) {
            // 创建单据主信息
            BasicDocumentInfo documentInfo = mapProductionPickingMaterialToDocumentInfo(picking);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);
            documentsToInsert.add(documentInfo);

            // 查询明细并创建单据明细
            List<ProductionPickingMaterialDetail> details = productionPickingMaterialDetailService.getDetailsByPickingId(picking.getId());

            for (ProductionPickingMaterialDetail detail : details) {
                BasicDocumentDetail documentDetail = mapProductionPickingMaterialDetailToDocumentDetail(detail, documentId);
                detailsToInsert.add(documentDetail);
            }
        }

        // 批量插入
        if (!documentsToInsert.isEmpty()) {
            this.saveBatch(documentsToInsert);
            logger.info("批量插入{}主单据 {} 条", sourceTypeName, documentsToInsert.size());
        }

        if (!detailsToInsert.isEmpty()) {
            basicDocumentDetailService.saveBatch(detailsToInsert);
            logger.info("批量插入{}明细 {} 条", sourceTypeName, detailsToInsert.size());
        }

        String summary = String.format("成功创建%s生产出库单据%d条，明细%d条", sourceTypeName, documentsToInsert.size(), detailsToInsert.size());
        logger.info(summary);

        return ResponseResult.getSuccessResult();
    }

    /**
     * 批量创建生产出库单据（生产补料单）
     *
     * @param productionFeedMaterials 生产补料单列表
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createProductionFeedOutboundDocumentsBatch(List<ProductionFeedMaterial> productionFeedMaterials) {
        logger.info("开始批量创建生产出库单据，生产补料单数量：{}", productionFeedMaterials.size());

        List<BasicDocumentInfo> documentsToInsert = new ArrayList<>();
        List<BasicDocumentDetail> detailsToInsert = new ArrayList<>();

        for (ProductionFeedMaterial feed : productionFeedMaterials) {
            // 创建单据主信息
            BasicDocumentInfo documentInfo = mapProductionFeedMaterialToDocumentInfo(feed);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);
            documentsToInsert.add(documentInfo);

            // 查询明细并创建单据明细
            List<ProductionFeedMaterialDetail> details = productionFeedMaterialDetailService.getDetailsByFeedId(feed.getId());

            for (ProductionFeedMaterialDetail detail : details) {
                BasicDocumentDetail documentDetail = mapProductionFeedMaterialDetailToDocumentDetail(detail, documentId);
                detailsToInsert.add(documentDetail);
            }
        }

        // 批量插入
        if (!documentsToInsert.isEmpty()) {
            this.saveBatch(documentsToInsert);
            logger.info("批量插入生产补料单主单据 {} 条", documentsToInsert.size());
        }

        if (!detailsToInsert.isEmpty()) {
            basicDocumentDetailService.saveBatch(detailsToInsert);
            logger.info("批量插入生产补料单明细 {} 条", detailsToInsert.size());
        }

        String summary = String.format("成功创建生产补料单生产出库单据%d条，明细%d条", documentsToInsert.size(), detailsToInsert.size());
        logger.info(summary);

        return ResponseResult.getSuccessResult();
    }

    /**
     * 批量创建生产入库单据
     *
     * @param productionInStocks 生产入库单列表
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createProductionInboundDocumentsBatch(List<ProductionInStock> productionInStocks) {
        logger.info("开始批量创建生产入库单据，生产入库单数量：{}", productionInStocks.size());

        List<BasicDocumentInfo> documentsToInsert = new ArrayList<>();
        List<BasicDocumentDetail> detailsToInsert = new ArrayList<>();

        for (ProductionInStock inStock : productionInStocks) {
            // 创建单据主信息
            BasicDocumentInfo documentInfo = mapProductionInStockToDocumentInfo(inStock);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);
            documentsToInsert.add(documentInfo);

            // 查询明细并创建单据明细
            List<ProductionInStockDetail> details = productionInStockDetailService.getDetailsByInStockId(inStock.getId());

            for (ProductionInStockDetail detail : details) {
                BasicDocumentDetail documentDetail = mapProductionInStockDetailToDocumentDetail(detail, documentId);
                detailsToInsert.add(documentDetail);
            }
        }

        // 批量插入
        if (!documentsToInsert.isEmpty()) {
            this.saveBatch(documentsToInsert);
            logger.info("批量插入生产入库单主单据 {} 条", documentsToInsert.size());
        }

        if (!detailsToInsert.isEmpty()) {
            basicDocumentDetailService.saveBatch(detailsToInsert);
            logger.info("批量插入生产入库单明细 {} 条", detailsToInsert.size());
        }

        String summary = String.format("成功创建生产入库单据%d条，明细%d条", documentsToInsert.size(), detailsToInsert.size());
        logger.info(summary);

        return ResponseResult.getSuccessResult();
    }

    /**
     * 批量创建简单生产出库单据
     *
     * @param simpleProductionPickingMaterials 简单生产领料单列表
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createSimpleProductionOutboundDocumentsBatch(List<SimpleProductionPickingMaterial> simpleProductionPickingMaterials) {
        logger.info("开始批量创建简单生产出库单据，简单生产领料单数量：{}", simpleProductionPickingMaterials.size());

        List<BasicDocumentInfo> documentsToInsert = new ArrayList<>();
        List<BasicDocumentDetail> detailsToInsert = new ArrayList<>();

        for (SimpleProductionPickingMaterial picking : simpleProductionPickingMaterials) {
            // 创建单据主信息
            BasicDocumentInfo documentInfo = mapSimpleProductionPickingMaterialToDocumentInfo(picking);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);
            documentsToInsert.add(documentInfo);

            // 查询明细并创建单据明细
            List<SimpleProductionPickingMaterialDetail> details = simpleProductionPickingMaterialDetailService.getDetailsByPickingId(picking.getId());

            for (SimpleProductionPickingMaterialDetail detail : details) {
                BasicDocumentDetail documentDetail = mapSimpleProductionPickingMaterialDetailToDocumentDetail(detail, documentId);
                detailsToInsert.add(documentDetail);
            }
        }

        // 批量插入
        if (!documentsToInsert.isEmpty()) {
            this.saveBatch(documentsToInsert);
            logger.info("批量插入简单生产领料单主单据 {} 条", documentsToInsert.size());
        }

        if (!detailsToInsert.isEmpty()) {
            basicDocumentDetailService.saveBatch(detailsToInsert);
            logger.info("批量插入简单生产领料单明细 {} 条", detailsToInsert.size());
        }

        String summary = String.format("成功创建简单生产领料单生产出库单据%d条，明细%d条", documentsToInsert.size(), detailsToInsert.size());
        logger.info(summary);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 批量创建简单生产入库单据
     *
     * @param simpleProductionInStocks 简单生产入库单列表
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult createSimpleProductionInboundDocumentsBatch(List<SimpleProductionInStock> simpleProductionInStocks) {
        logger.info("开始批量创建简单生产入库单据，简单生产入库单数量：{}", simpleProductionInStocks.size());

        List<BasicDocumentInfo> documentsToInsert = new ArrayList<>();
        List<BasicDocumentDetail> detailsToInsert = new ArrayList<>();

        for (SimpleProductionInStock inStock : simpleProductionInStocks) {
            // 创建单据主信息
            BasicDocumentInfo documentInfo = mapSimpleProductionInStockToDocumentInfo(inStock);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);
            documentsToInsert.add(documentInfo);

            // 查询明细并创建单据明细
            List<SimpleProductionInStockDetail> details = simpleProductionInStockDetailService.getDetailsByInStockId(inStock.getId());

            for (SimpleProductionInStockDetail detail : details) {
                BasicDocumentDetail documentDetail = mapSimpleProductionInStockDetailToDocumentDetail(detail, documentId);
                detailsToInsert.add(documentDetail);
            }
        }

        // 批量插入
        if (!documentsToInsert.isEmpty()) {
            this.saveBatch(documentsToInsert);
            logger.info("批量插入简单生产入库单主单据 {} 条", documentsToInsert.size());
        }

        if (!detailsToInsert.isEmpty()) {
            basicDocumentDetailService.saveBatch(detailsToInsert);
            logger.info("批量插入简单生产入库单明细 {} 条", detailsToInsert.size());
        }

        String summary = String.format("成功创建简单生产入库单据%d条，明细%d条", documentsToInsert.size(), detailsToInsert.size());
        logger.info(summary);

        return ResponseResult.getSuccessResult();
    }

    // ==================== 映射方法 ====================

    /**
     * 将生产领料单映射为单据主信息
     */
    private BasicDocumentInfo mapProductionPickingMaterialToDocumentInfo(ProductionPickingMaterial picking) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.SCLL);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(picking.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.OUT); // 出库
        documentInfo.setBusinessType(CommonConstant.BoundType.SCLL); // 生产领料
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定

        return documentInfo;
    }

    /**
     * 将生产领料单明细映射为单据明细
     */
    private BasicDocumentDetail mapProductionPickingMaterialDetailToDocumentDetail(ProductionPickingMaterialDetail detail, String documentCode) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setDocumentCode(documentCode);
        documentDetail.setMaterialCode(detail.getMaterialId());

        // 数量转换：BigDecimal转Integer
        Integer quantity = (detail.getActualQty() != null) ? detail.getActualQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0);

        return documentDetail;
    }

    /**
     * 将生产补料单映射为单据主信息
     */
    private BasicDocumentInfo mapProductionFeedMaterialToDocumentInfo(ProductionFeedMaterial feed) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.SCBL);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(feed.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.OUT); // 出库
        documentInfo.setBusinessType(CommonConstant.BoundType.SCBL); // 生产补料
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定

        return documentInfo;
    }

    /**
     * 将生产补料单明细映射为单据明细
     */
    private BasicDocumentDetail mapProductionFeedMaterialDetailToDocumentDetail(ProductionFeedMaterialDetail detail, String documentCode) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentCode);
        documentDetail.setMaterialCode(detail.getMaterialId());

        // 数量转换：BigDecimal转Integer
        Integer quantity = (detail.getActualQty() != null) ? detail.getActualQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0);

        return documentDetail;
    }

    /**
     * 将生产入库单映射为单据主信息
     */
    private BasicDocumentInfo mapProductionInStockToDocumentInfo(ProductionInStock inStock) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.SCRK);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(inStock.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.IN); // 入库
        documentInfo.setBusinessType(CommonConstant.BoundType.SCRK); // 生产入库
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定

        return documentInfo;
    }

    /**
     * 将生产入库单明细映射为单据明细
     */
    private BasicDocumentDetail mapProductionInStockDetailToDocumentDetail(ProductionInStockDetail detail, String documentCode) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentCode);
        documentDetail.setMaterialCode(detail.getMaterialId());

        // 数量转换：BigDecimal转Integer
        Integer quantity = (detail.getRealQty() != null) ? detail.getRealQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0);

        return documentDetail;
    }

    /**
     * 将简单生产领料单映射为单据主信息
     */
    private BasicDocumentInfo mapSimpleProductionPickingMaterialToDocumentInfo(SimpleProductionPickingMaterial picking) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.SCLL);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(picking.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.OUT); // 出库
        documentInfo.setBusinessType(CommonConstant.BoundType.SCLL); // 生产领料
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定

        return documentInfo;
    }

    /**
     * 将简单生产领料单明细映射为单据明细
     */
    private BasicDocumentDetail mapSimpleProductionPickingMaterialDetailToDocumentDetail(SimpleProductionPickingMaterialDetail detail, String documentCode) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentCode);
        documentDetail.setMaterialCode(detail.getMaterialId());

        // 数量转换：BigDecimal转Integer
        Integer quantity = (detail.getActualQty() != null) ? detail.getActualQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0);

        return documentDetail;
    }

    /**
     * 将简单生产入库单映射为单据主信息
     */
    private BasicDocumentInfo mapSimpleProductionInStockToDocumentInfo(SimpleProductionInStock inStock) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.SCRK);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(inStock.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.IN); // 入库
        documentInfo.setBusinessType(CommonConstant.BoundType.SCRK); // 生产入库
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定

        return documentInfo;
    }

    /**
     * 将简单生产入库单明细映射为单据明细
     */
    private BasicDocumentDetail mapSimpleProductionInStockDetailToDocumentDetail(SimpleProductionInStockDetail detail, String documentCode) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentCode);
        documentDetail.setMaterialCode(detail.getMaterialId());

        // 数量转换：BigDecimal转Integer
        Integer quantity = (detail.getRealQty() != null) ? detail.getRealQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0);

        return documentDetail;
    }

    /**
     * 从收料通知单生成采购入库单
     *
     * @param receiveNotice 收料通知单
     * @return 生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult generatePurchaseInStockFromReceiveNotice(ReceiveNotice receiveNotice) {
        logger.info("开始为收料通知单 {} 生成采购入库单", receiveNotice.getBillNo());

        try {
            // 1. 创建单据主信息
            BasicDocumentInfo documentInfo = mapReceiveNoticeToDocument(receiveNotice);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);

            // 2. 查询收料通知单明细
            List<ReceiveNoticeDetail> noticeDetails = receiveNoticeDetailService.getDetailsByNoticeId(receiveNotice.getId());

            if (noticeDetails.isEmpty()) {
                logger.warn("收料通知单 {} 没有明细，跳过生成采购入库单", receiveNotice.getBillNo());
                return ResponseResult.getSuccessResult("收料通知单没有明细，跳过生成", null);
            }

            // 3. 创建单据明细
            List<BasicDocumentDetail> documentDetails = new ArrayList<>();
            for (ReceiveNoticeDetail noticeDetail : noticeDetails) {
                BasicDocumentDetail detail = mapReceiveNoticeDetailToDocumentDetail(noticeDetail, documentId);
                documentDetails.add(detail);
            }

            // 4. 保存单据主信息
            basicDocumentInfoMapper.insert(documentInfo);

            // 5. 保存单据明细
            for (BasicDocumentDetail detail : documentDetails) {
                basicDocumentDetailMapper.insert(detail);
            }

            logger.info("成功为收料通知单 {} 生成采购入库单，明细数量：{}",
                    receiveNotice.getBillNo(), documentDetails.size());

            return ResponseResult.getSuccessResult(
                    String.format("成功生成采购入库单，明细数量：%d", documentDetails.size()),
                    documentInfo.getId());

        } catch (Exception e) {
            logger.error("为收料通知单 {} 生成采购入库单失败", receiveNotice.getBillNo(), e);
            throw e; // 让事务回滚
        }
    }

    /**
     * 将收料通知单映射为单据主信息
     *
     * @param receiveNotice 收料通知单
     * @return 单据主信息
     */
    private BasicDocumentInfo mapReceiveNoticeToDocument(ReceiveNotice receiveNotice) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.CGRK);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(receiveNotice.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.IN); // 入库
        documentInfo.setBusinessType(CommonConstant.BoundType.CGRK);  // 采购入库
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定
        documentInfo.setSupplySalesCode(receiveNotice.getSupplierId());

        return documentInfo;
    }

    /**
     * 将收料通知单明细映射为单据明细
     *
     * @param noticeDetail 收料通知单明细
     * @param documentId   单据ID
     * @return 单据明细
     */
    private BasicDocumentDetail mapReceiveNoticeDetailToDocumentDetail(ReceiveNoticeDetail noticeDetail, String documentId) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentId);
        documentDetail.setMaterialCode(noticeDetail.getMaterialId());

        // 数量转换：使用实到数量，BigDecimal转Integer
        Integer quantity = (noticeDetail.getActReceiveQty() != null) ?
                noticeDetail.getActReceiveQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0); // 初始完成数量为0

        logger.debug("收料通知单明细转换完成，物料编码：{}，数量：{}",
                noticeDetail.getMaterialId(), quantity);

        return documentDetail;
    }

    /**
     * 从退料申请单生成采购出库单
     *
     * @param returnMaterial 退料申请单
     * @return 生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult generatePurchaseOutStockFromReturnMaterial(ReturnMaterial returnMaterial) {
        logger.info("开始为退料申请单 {} 生成采购出库单", returnMaterial.getBillNo());

        try {
            // 1. 创建单据主信息
            BasicDocumentInfo documentInfo = mapReturnMaterialToDocument(returnMaterial);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);

            // 2. 查询退料申请单明细
            List<ReturnMaterialDetail> appDetails = returnMaterialDetailService.getDetailsByAppId(returnMaterial.getId());

            if (appDetails.isEmpty()) {
                logger.warn("退料申请单 {} 没有明细，跳过生成采购出库单", returnMaterial.getBillNo());
                return ResponseResult.getSuccessResult("退料申请单没有明细，跳过生成", null);
            }

            // 3. 创建单据明细
            List<BasicDocumentDetail> documentDetails = new ArrayList<>();
            for (ReturnMaterialDetail appDetail : appDetails) {
                BasicDocumentDetail detail = mapReturnMaterialDetailToDocumentDetail(appDetail, documentId);
                documentDetails.add(detail);
            }

            // 4. 保存单据主信息
            basicDocumentInfoMapper.insert(documentInfo);

            // 5. 保存单据明细
            for (BasicDocumentDetail detail : documentDetails) {
                basicDocumentDetailMapper.insert(detail);
            }

            logger.info("成功为退料申请单 {} 生成采购出库单，明细数量：{}",
                    returnMaterial.getBillNo(), documentDetails.size());

            return ResponseResult.getSuccessResult(
                    String.format("成功生成采购出库单，明细数量：%d", documentDetails.size()),
                    documentInfo.getId());

        } catch (Exception e) {
            logger.error("为退料申请单 {} 生成采购出库单失败", returnMaterial.getBillNo(), e);
            throw e; // 让事务回滚
        }
    }

    /**
     * 将退料申请单映射为单据主信息
     *
     * @param returnMaterial 退料申请单
     * @return 单据主信息
     */
    private BasicDocumentInfo mapReturnMaterialToDocument(ReturnMaterial returnMaterial) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.CGTH);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(returnMaterial.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.OUT); // 出库
        documentInfo.setBusinessType(CommonConstant.BoundType.CGTH);  // 采购退货出库
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定
        documentInfo.setSupplySalesCode(returnMaterial.getSupplierId());

        return documentInfo;
    }

    /**
     * 将退料申请单明细映射为单据明细
     *
     * @param appDetail  退料申请单明细
     * @param documentId 单据ID
     * @return 单据明细
     */
    private BasicDocumentDetail mapReturnMaterialDetailToDocumentDetail(ReturnMaterialDetail appDetail, String documentId) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentId);
        documentDetail.setMaterialCode(appDetail.getMaterialId());

        // 数量转换：使用申请退料数量，BigDecimal转Integer
        Integer quantity = (appDetail.getMrAppQty() != null) ?
                appDetail.getMrAppQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0); // 初始完成数量为0

        logger.debug("退料申请单明细转换完成，物料编码：{}，数量：{}",
                appDetail.getMaterialId(), quantity);

        return documentDetail;
    }

    /**
     * 从退货通知单生成销售退货入库单
     *
     * @param returnNotice 退货通知单
     * @return 生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult generateSaleReturnInStockFromReturnNotice(ReturnNotice returnNotice) {
        logger.info("开始为退货通知单 {} 生成销售退货入库单", returnNotice.getBillNo());

        try {
            // 1. 创建单据主信息
            BasicDocumentInfo documentInfo = mapReturnNoticeToDocument(returnNotice);
            String documentId = UUID.randomUUID().toString();
            documentInfo.setId(documentId);

            // 2. 查询退货通知单明细
            List<ReturnNoticeDetail> noticeDetails = returnNoticeDetailService.getDetailsByNoticeId(returnNotice.getId());

            if (noticeDetails.isEmpty()) {
                logger.warn("退货通知单 {} 没有明细，跳过生成销售退货入库单", returnNotice.getBillNo());
                return ResponseResult.getSuccessResult("退货通知单没有明细，跳过生成", null);
            }

            // 3. 创建单据明细
            List<BasicDocumentDetail> documentDetails = new ArrayList<>();
            for (ReturnNoticeDetail noticeDetail : noticeDetails) {
                BasicDocumentDetail detail = mapReturnNoticeDetailToDocumentDetail(noticeDetail, documentId);
                documentDetails.add(detail);
            }

            // 4. 保存单据主信息
            basicDocumentInfoMapper.insert(documentInfo);

            // 5. 保存单据明细
            for (BasicDocumentDetail detail : documentDetails) {
                basicDocumentDetailMapper.insert(detail);
            }

            logger.info("成功为退货通知单 {} 生成销售退货入库单，明细数量：{}",
                    returnNotice.getBillNo(), documentDetails.size());

            return ResponseResult.getSuccessResult(
                    String.format("成功生成销售退货入库单，明细数量：%d", documentDetails.size()),
                    documentInfo.getId());

        } catch (Exception e) {
            logger.error("为退货通知单 {} 生成销售退货入库单失败", returnNotice.getBillNo(), e);
            throw e; // 让事务回滚
        }
    }

    /**
     * 将退货通知单映射为单据主信息
     *
     * @param returnNotice 退货通知单
     * @return 单据主信息
     */
    private BasicDocumentInfo mapReturnNoticeToDocument(ReturnNotice returnNotice) {
        BasicDocumentInfo documentInfo = new BasicDocumentInfo();

        // 生成WMS内部单据编号
        String prefix = getPrefixByType(CommonConstant.BoundType.XSTH);
        String transactionCode = getMaxIndex(prefix);
        documentInfo.setTransactionCode(transactionCode);

        // 保存ERP来源单据编号
        documentInfo.setSourceDocumentNo(returnNotice.getBillNo());

        documentInfo.setTransactionType(CommonConstant.InoutType.IN); // 入库
        documentInfo.setBusinessType(CommonConstant.BoundType.XSTH);  // 销售退货入库
        documentInfo.setBusinessSource(CommonConstant.BusinessSource.ERP); // ERP来源
        documentInfo.setReceTime(DateAndTimeUtil.getNowDate());
        documentInfo.setStatus(CommonConstant.DocumentStatus.UN_FINISH); // 未完成
        documentInfo.setIsLock(CommonConstant.IsLock.LOCK); // 锁定
        documentInfo.setSupplySalesCode(returnNotice.getRetCustId());

        return documentInfo;
    }

    /**
     * 将退货通知单明细映射为单据明细
     *
     * @param noticeDetail 退货通知单明细
     * @param documentId   单据ID
     * @return 单据明细
     */
    private BasicDocumentDetail mapReturnNoticeDetailToDocumentDetail(ReturnNoticeDetail noticeDetail, String documentId) {
        BasicDocumentDetail documentDetail = new BasicDocumentDetail();

        documentDetail.setId(UUID.randomUUID().toString());
        documentDetail.setDocumentCode(documentId);
        documentDetail.setMaterialCode(noticeDetail.getMaterialId());

        // 数量转换：使用销售数量，BigDecimal转Integer
        Integer quantity = (noticeDetail.getQty() != null) ?
                noticeDetail.getQty().intValue() : 0;
        documentDetail.setQuantity(quantity);
        documentDetail.setCompletedNum(0); // 初始完成数量为0

        logger.debug("退货通知单明细转换完成，物料编码：{}，数量：{}",
                noticeDetail.getMaterialId(), quantity);

        return documentDetail;
    }

    //***********************************************确认来料功能**********************************************

    /**
     * 确认到货（通用方法，支持入库和出库场景）
     * 入库场景：确认来料到货
     * 出库场景：确认备货完成
     * @param documentId 单据ID
     * @param arrivalList 到货明细列表
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult confirmArrival(String documentId, List<MaterialArrivalDto> arrivalList) {
        try {
            // 1. 验证单据状态
            BasicDocumentInfo documentInfo = basicDocumentInfoMapper.selectById(documentId);
            if (documentInfo == null) {
                return ResponseResult.getErrorResult("单据不存在");
            }
            if (documentInfo.getIsLock() != CommonConstant.IsLock.LOCK) {
                return ResponseResult.getErrorResult("单据未锁定，无法确认到货");
            }

            // 2. 处理每个物料
            for (MaterialArrivalDto arrival : arrivalList) {
                // 验证数据
                ResponseResult validationResult = validateArrivalData(arrival, documentInfo);
                if (!validationResult.getCode().equals(ResultMsg.successCode)) {
                    return validationResult;
                }
                // 生成批次处理记录
                createBatchProcessRecord(arrival, documentInfo);
                // 生成质检任务（如果需要）
                generateQcTaskIfNeeded(arrival, documentInfo);
                // 更新单据明细状态
                updateDocumentDetailArrivalStatus(arrival);
            }

            String operationType = documentInfo.getTransactionType() == CommonConstant.InoutType.IN ? "来料确认" : "备货确认";
            logger.info("{}成功，单据ID：{}", operationType, documentId);
            return ResponseResult.getSuccessResult();

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 验证到货数据
     * 入库场景：验证来料数据
     * 出库场景：验证备货数据
     *  注意：总到货数量可以超过计划数量（因为供应商可能多发货，或者包含不合格品）
     */
    private ResponseResult validateArrivalData(MaterialArrivalDto arrival, BasicDocumentInfo documentInfo) {
        BasicDocumentDetail detail = basicDocumentDetailMapper.selectById(arrival.getDetailId());
        if (detail == null) {
            return ResponseResult.getErrorResult("单据明细不存在，ID：" + arrival.getDetailId());
        }

        if (arrival.getArrivalQuantity() == null || arrival.getArrivalQuantity() <= 0) {
            String quantityType = documentInfo.getTransactionType() == CommonConstant.InoutType.IN ? "来料数量" : "备货数量";
            return ResponseResult.getErrorResult("物料编码:"+arrival.getMaterialCode()+","+quantityType + "必须大于0");
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 生成批次处理记录
     * 入库场景：生成批次入库记录
     * 出库场景：生成批次出库记录
     */
    private void createBatchProcessRecord(MaterialArrivalDto arrival, BasicDocumentInfo documentInfo) {
        DocumentInventoryDetail inventoryDetail = new DocumentInventoryDetail();
        inventoryDetail.setId(UUID.randomUUID().toString());
        inventoryDetail.setDetailCode(arrival.getDetailId());
        inventoryDetail.setMaterialCode(arrival.getMaterialCode());
        inventoryDetail.setQuantity(arrival.getArrivalQuantity());
        inventoryDetail.setCompletedNum(0); // 初始未入库

        // 处理批次号：手动输入优先，否则自动生成
        String batchNumber;
        if (StringUtils.isNotEmpty(arrival.getBatch())) {
            // 使用手动输入的批次号
            batchNumber = arrival.getBatch();
        } else {
            // 自动生成批次号
            batchNumber = generateBatchNumber("BATCH");
        }
        inventoryDetail.setInventoryCode(batchNumber);

        // 根据物料是否需要质检设置初始状态
        if (needQualityCheck(arrival.getMaterialCode(), documentInfo)) {
            inventoryDetail.setBatchStatus(DocumentStatusConstant.BatchStatus.PENDING_QC); // 待质检
        } else {
            inventoryDetail.setBatchStatus(DocumentStatusConstant.BatchStatus.PENDING_PROCESS); // 无需质检，直接待处理
        }

        documentInventoryDetailMapper.insert(inventoryDetail);
        String recordType = documentInfo.getTransactionType() == CommonConstant.InoutType.IN ? "批次入库记录" : "批次出库记录";
        logger.info("创建{}，物料：{}，批次：{}，数量：{}，状态：{}",
            recordType, arrival.getMaterialCode(), batchNumber, arrival.getArrivalQuantity(), inventoryDetail.getBatchStatus());
    }

    /**
     * 检查物料是否需要质检
     * @param materialCode 物料编码
     * @param documentInfo 单据信息
     * @return true-需要质检，false-不需要质检
     */
    private boolean needQualityCheck(String materialCode, BasicDocumentInfo documentInfo) {
        try {
            // 根据单据类型确定质检类型
            Integer qcType = getQcTypeByTransactionType(documentInfo.getTransactionType());
            // 检查物料是否绑定了对应的启用质检模板
            QcTemplateMaterial qcTemplateMaterial = qcTemplateMaterialService.getTemplateMaterialByMaterialCodeAndQcType(materialCode, qcType);
            if (qcTemplateMaterial == null) {
                logger.info("物料{}未配置<{}>的启用质检模板，无需质检", materialCode, getQcTypeDescription(qcType));
                return false;
            }
            logger.info("物料{}需要质检，质检类型：{}，模板编码：{}", materialCode, getQcTypeDescription(qcType), qcTemplateMaterial.getTemplateCode());
            return true;

        } catch (Exception e) {
            logger.error("检查物料{}质检模板时发生异常", materialCode);
            return false;
        }
    }

    /**
     * 根据单据出入库类型确定质检类型
     * @param transactionType 单据出入库类型
     * @return 质检类型，null表示不需要质检
     */
    private Integer getQcTypeByTransactionType(Integer transactionType) {
        if (transactionType == null) {
            return null;
        }
        if (transactionType.equals(CommonConstant.InoutType.OUT)) {
            return 4; // 出库质检
        }
        if (transactionType.equals(CommonConstant.InoutType.IN)) {
            return 1; // 来料质检
        }
        return null;
    }

    /**
     * 生成质检任务（如果需要）
     * @param arrival 到货信息
     * @param documentInfo 单据信息
     */
    private void generateQcTaskIfNeeded(MaterialArrivalDto arrival, BasicDocumentInfo documentInfo) {
        if (!needQualityCheck(arrival.getMaterialCode(), documentInfo)) {
            return;
        }

        try {
            Integer qcType = getQcTypeByTransactionType(documentInfo.getTransactionType());
            // 确定源任务类型描述
            String sourceTaskType = getSourceTaskTypeDescription(documentInfo.getBusinessType(), documentInfo.getTransactionType());

            // 生成质检任务
            qcWorkTaskService.saveQcIpqcInfo(
                arrival.getMaterialCode(),           // 物料编码
                arrival.getArrivalQuantity(),        // 质检数量
                documentInfo.getSupplySalesCode(),   // 供应商/客户编码
                sourceTaskType,                      // 源任务类型
                documentInfo.getTransactionCode(),   // 来源单据号
                qcType                              // 质检类型
            );

            logger.info("成功生成质检任务 - 物料：{}，数量：{}，来源单据：{}，供应商：{}",
                arrival.getMaterialCode(),
                arrival.getArrivalQuantity(),
                documentInfo.getTransactionCode(),
                documentInfo.getSupplySalesCode());

        } catch (Exception e) {
            logger.error("生成质检任务失败 - 物料：{}，数量：{}，来源单据：{}",
                arrival.getMaterialCode(),
                arrival.getArrivalQuantity(),
                documentInfo.getTransactionCode(), e);
        }
    }

    /**
     * 根据业务类型和出入库类型获取源任务类型描述
     * @param businessType 业务类型
     * @param transactionType 出入库类型（1-入库，2-出库）
     * @return 源任务类型描述
     */
    private String getSourceTaskTypeDescription(Integer businessType, Integer transactionType) {
        if (businessType == null || transactionType == null) {
            return "质检";
        }
        // todo 描述需要定义常量类
        // 根据出入库类型和业务类型组合确定描述
        //     * 单据类型:1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库 9物料调拨 10库存盘点 11仓库用料
        if (transactionType == CommonConstant.InoutType.IN) { // 入库
            switch (businessType) {
                case 1: // CGRK - 采购入库
                    return "采购入库质检";
                case 2: // SCRK - 生产入库
                    return "生产入库质检";
                case 3: // QTRK - 其他入库
                    return "其他入库质检";
                case 4: // TGRK - 退货入库
                    return "退货入库质检";
                default:
                    return "入库质检";
            }
        } else if (transactionType == CommonConstant.InoutType.OUT) { // 出库
            switch (businessType) {
                case 1: // CGCK - 采购出库（退货）
                    return "采购退货质检";
                case CommonConstant.BoundType.SCLL: // SCCK - 生产出库（领料）
                    return "生产领料质检";
                case 3: // QTCK - 其他出库
                    return "其他出库质检";
                case 4: // XSCK - 销售出库
                    return "销售出库质检";
                default:
                    return "出库质检";
            }
        }

        return "质检";
    }

    /**
     * 获取质检类型的中文描述
     * @param qcType 质检类型
     * @return 中文描述
     */
    private String getQcTypeDescription(Integer qcType) {
        if (qcType == null) {
            return "未知质检类型";
        }
        switch (qcType) {
            case 1:
                return "来料质检";
            case 4:
                return "出库质检";
            default:
                return "" ;
        }
    }

    /**
     * 更新单据明细状态
     */
    private void updateDocumentDetailArrivalStatus(MaterialArrivalDto arrival) {
        BasicDocumentDetail detail = basicDocumentDetailMapper.selectById(arrival.getDetailId());

        // 累加到总到货数量
        Integer currentTotal = detail.getTotalArrivalNum() != null ? detail.getTotalArrivalNum() : 0;
        detail.setTotalArrivalNum(currentTotal + arrival.getArrivalQuantity());

        // 根据批次完成情况更新明细任务状态
        updateDetailTaskStatusByBatches(arrival.getDetailId());

        basicDocumentDetailMapper.updateById(detail);
        logger.info("更新单据明细状态，明细ID：{}，累加总到货数量：{}",
            arrival.getDetailId(), detail.getTotalArrivalNum());
    }

    /**
     * 根据批次完成情况更新明细任务状态
     */
    private void updateDetailTaskStatusByBatches(String detailId) {
        BasicDocumentDetail detail = basicDocumentDetailMapper.selectById(detailId);
        // 查询该明细的所有批次记录
        List<DocumentInventoryDetail> batches = documentInventoryDetailMapper.selectByDetailCode(detailId);
        if (batches.isEmpty()) {
            // 没有批次记录，保持待处理状态
            detail.setTaskStatus(DocumentStatusConstant.TaskStatus.PENDING);
        } else {
            // 计算已完成的数量
            int completedQuantity = batches.stream()
                .filter(batch -> batch.getBatchStatus() == DocumentStatusConstant.BatchStatus.PROCESSED)
                .mapToInt(batch -> batch.getCompletedNum() != null ? batch.getCompletedNum() : 0)
                .sum();

            if (completedQuantity == 0) {
                // 没有完成的批次，设为待处理
                detail.setTaskStatus(DocumentStatusConstant.TaskStatus.PENDING);
            } else if (completedQuantity < detail.getQuantity()) {
                // 部分完成，设为处理中
                detail.setTaskStatus(DocumentStatusConstant.TaskStatus.PROCESSING);
            } else {
                // 全部完成
                detail.setTaskStatus(DocumentStatusConstant.TaskStatus.COMPLETED);
            }
        }

        basicDocumentDetailMapper.updateById(detail);
    }
}